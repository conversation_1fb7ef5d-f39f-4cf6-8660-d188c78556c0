# Vanna + Ecovai 网页演示
# 使用Flask创建一个直观的网页界面来演示SQL生成功能

import os
from dotenv import load_dotenv
load_dotenv()

from openai import OpenAI
import pandas as pd
import sqlite3
from pathlib import Path

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.openai.openai_embeddings import OpenAI_Embeddings
from vanna.flask import VannaFlaskApp

# 构建Ecovai客户端
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

client = OpenAI(api_key=api_key, base_url=base_url)

# 创建ECOVai Vanna类
class ECOVaiVanna(OpenAI_Chat, OpenAI_Embeddings):
    def __init__(self, client=None, config=None):
        OpenAI_Chat.__init__(self, client=client, config=config)
        OpenAI_Embeddings.__init__(self, client=client, config=config)

    def generate_embedding(self, data: str, **kwargs):
        """使用Ecovai的embedding模型生成向量"""
        try:
            resp = self.client.embeddings.create(model=embedding_model, input=data)
            return resp.data[0].embedding
        except Exception as e:
            print(f"生成embedding时出错: {e}")
            return None

    # 实现必需的抽象方法（简化版本）
    def get_similar_question_sql(self, question: str, **kwargs):
        return []

    def get_related_ddl(self, question: str, **kwargs):
        return []

    def get_related_documentation(self, question: str, **kwargs):
        return []

    def add_question_sql(self, question: str, sql: str, **kwargs):
        return 'added'

    def add_ddl(self, ddl: str, **kwargs):
        return 'added'

    def add_documentation(self, documentation: str, **kwargs):
        return 'added'

    def get_training_data(self, **kwargs):
        return pd.DataFrame()

    def remove_training_data(self, id: str, **kwargs):
        return True

# 实例化Vanna
vn = ECOVaiVanna(client=client, config={'model': text_model})

# 连接到Chinook数据库
chinook_path = Path('.') / 'Chinook.sqlite'
if not chinook_path.exists():
    import urllib.request
    print('正在下载Chinook.sqlite数据库...')
    urllib.request.urlretrieve('https://vanna.ai/Chinook.sqlite', str(chinook_path))

# 连接数据库
vn.connect_to_sqlite(str(chinook_path))

# 创建Flask应用
app = VannaFlaskApp(
    vn=vn,
    debug=True,
    allow_llm_to_see_data=True,
    logo="https://img.vanna.ai/vanna-flask.svg",
    title="Vanna + Ecovai SQL生成器",
    subtitle="使用Ecovai API的智能SQL查询助手",
    show_training_data=True,
    suggested_questions=True,
    sql=True,
    table=True,
    csv_download=True,
    chart=True,
    redraw_chart=True,
    auto_fix_sql=True,
    ask_results_correct=True,
    followup_questions=True,
    summarization=True,
)

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 Vanna + Ecovai SQL生成器启动中...")
    print("=" * 60)
    print("📊 数据库: Chinook (音乐商店数据)")
    print("🤖 AI模型: Ecovai " + text_model)
    print("🔗 访问地址: http://localhost:8084")
    print("=" * 60)
    print("💡 你可以尝试问这些问题:")
    print("   - 销售额最高的前10位艺术家是谁？")
    print("   - 每个国家的年度总销售额是多少？")
    print("   - 哪个流派的销售额最高？")
    print("   - 最受欢迎的专辑有哪些？")
    print("=" * 60)
    
    app.run()
