# Vanna + Ecovai 网页演示
# 使用Flask创建一个直观的网页界面来演示SQL生成功能

import os
from dotenv import load_dotenv
load_dotenv()

from openai import OpenAI
import pandas as pd
import sqlite3
from pathlib import Path

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.openai.openai_embeddings import OpenAI_Embeddings
from vanna.flask import VannaFlaskApp
from flask import render_template, request

# 构建Ecovai客户端
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

client = OpenAI(api_key=api_key, base_url=base_url)

# 创建ECOVai Vanna类
class ECOVaiVanna(OpenAI_Chat, OpenAI_Embeddings):
    def __init__(self, client=None, config=None):
        OpenAI_Chat.__init__(self, client=client, config=config)
        OpenAI_Embeddings.__init__(self, client=client, config=config)

    def generate_embedding(self, data: str, **kwargs):
        """使用Ecovai的embedding模型生成向量"""
        try:
            resp = self.client.embeddings.create(model=embedding_model, input=data)
            return resp.data[0].embedding
        except Exception as e:
            print(f"生成embedding时出错: {e}")
            return None

    # 实现必需的抽象方法（简化版本）
    def get_similar_question_sql(self, question: str, **kwargs):
        return []

    def get_related_ddl(self, question: str, **kwargs):
        return []

    def get_related_documentation(self, question: str, **kwargs):
        return []

    def add_question_sql(self, question: str, sql: str, **kwargs):
        return 'added'

    def add_ddl(self, ddl: str, **kwargs):
        return 'added'

    def add_documentation(self, documentation: str, **kwargs):
        return 'added'

    def get_training_data(self, **kwargs):
        return pd.DataFrame()

    def remove_training_data(self, id: str, **kwargs):
        return True

# 实例化Vanna
vn = ECOVaiVanna(client=client, config={'model': text_model})

# 连接到Chinook数据库
chinook_path = Path('.') / 'Chinook.sqlite'
if not chinook_path.exists():
    import urllib.request
    print('正在下载Chinook.sqlite数据库...')
    urllib.request.urlretrieve('https://vanna.ai/Chinook.sqlite', str(chinook_path))

# 连接数据库
vn.connect_to_sqlite(str(chinook_path))

# Also open a local sqlite3 connection for direct queries from this Flask app
conn = sqlite3.connect(str(chinook_path), check_same_thread=False)

# 创建Flask应用
vapp = VannaFlaskApp(
    vn=vn,
    debug=True,
    allow_llm_to_see_data=True,
    logo="https://img.vanna.ai/vanna-flask.svg",
    title="Vanna + Ecovai SQL生成器",
    subtitle="使用Ecovai API的智能SQL查询助手",
    show_training_data=True,
    suggested_questions=True,
    sql=True,
    table=True,
    csv_download=True,
    chart=True,
    redraw_chart=True,
    auto_fix_sql=True,
    ask_results_correct=True,
    followup_questions=True,
    summarization=True,
)

# expose the real Flask app for route decorators
flask_app = vapp.flask_app

# Helper: introspect schema (tables and columns) and format a compact description
def introspect_schema(connection) -> str:
    cur = connection.cursor()
    tables = []
    for row in cur.execute("SELECT name FROM sqlite_master WHERE type='table';"):
        tables.append(row[0])
    schema_lines = []
    for t in tables:
        try:
            cols = [r[1] for r in cur.execute(f"PRAGMA table_info('{t}')")]
        except Exception:
            cols = []
        schema_lines.append(f"- {t}: {', '.join(cols)}")
    if not schema_lines:
        return 'No tables found in database.'
    return 'Database schema:\n' + '\n'.join(schema_lines)


# Build a filtered schema focusing on sales-related tables to improve SQL generation
def filter_schema_for_sales(schema_text: str) -> str:
    allowed = ['Invoice', 'InvoiceLine', 'Customer']
    lines = schema_text.splitlines()
    filtered = [ln for ln in lines if any(ln.startswith(f'- {t}:') for t in allowed)]
    if filtered:
        return '\n'.join(filtered)
    return schema_text

schema_text = introspect_schema(conn)
used_schema_text = filter_schema_for_sales(schema_text)


# Extract SQL helper (handles ```sql``` code fences and first SELECT fallback)
def extract_sql(text: str) -> str:
    import re
    if not text:
        return ''
    # find ```sql ... ``` blocks
    m = re.search(r"```sql\s*(.*?)```", text, re.S | re.I)
    if m:
        return m.group(1).strip()
    # find any ``` ... ``` block
    m = re.search(r"```\s*(.*?)```", text, re.S)
    if m:
        return m.group(1).strip()
    # fallback: find first SELECT ... ;
    m = re.search(r"(SELECT[\s\S]*?;)", text, re.I)
    if m:
        return m.group(1).strip()
    # otherwise return the raw text
    return text.strip()

# SQL safety check
import re
forbidden_re = re.compile(r"\b(drop|delete|update|insert|alter|create)\b", re.I)
def is_safe_sql(sql: str) -> bool:
    if not sql:
        return False
    sql_low = sql.strip().lower()
    if not sql_low.startswith('select'):
        return False
    if forbidden_re.search(sql):
        return False
    return True

# add plotly import for visualization
import plotly.express as px

@flask_app.route('/', methods=['GET', 'POST'])
def index():
    if vn and vn.client:
        question = None
        sql = None
        result_html = None
        error = None
        emb = None
        plot_html = None

        if request.method == 'POST':
            question = request.form.get('question')
            raw = request.form.get('raw_sql')
            sql = None

            # 系统消息，指示角色和任务
            system_base = 'You are a helpful SQL generation assistant. Use the provided database schema and generate SQL that matches the schema. Only return the SQL query inside a single ```sql``` code block if possible. Use only the given tables and columns.'
            system_with_schema = system_base + '\n\n' + used_schema_text

            # 用户问题和上下文
            user_question = f"问题: {question}\n\n请根据上述问题生成SQL查询："

            # 调用Vanna生成SQL
            try:
                response = vn.chat(
                    messages=[
                        {"role": "system", "content": system_with_schema},
                        {"role": "user", "content": user_question},
                    ],
                    temperature=0,
                    max_tokens=150,
                )
                generated_sql = response['choices'][0]['message']['content'].strip()
            except Exception as e:
                generated_sql = ''
                error = f"生成SQL时出错: {e}"

            # 执行SQL并获取结果
            # prefer user-provided raw SQL if present, otherwise use model-generated SQL
            if raw:
                sql = extract_sql(raw)
            else:
                sql = extract_sql(generated_sql)
            result_html = None
            plot_html = None
            error = None
            if sql and sql.strip().lower().startswith('select'):
                if not is_safe_sql(sql):
                    error = 'SQL 被拒绝：包含非 SELECT 或不允许的操作。'
                else:
                    try:
                        df = pd.read_sql_query(sql, conn)
                        result_html = df.to_html(index=False)
                        # simple visualization: if numeric column 'Total' or 'TotalSales' exists, plot top countries
                        numeric_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
                        if 'TotalSales' in df.columns or 'Total' in df.columns:
                            val_col = 'TotalSales' if 'TotalSales' in df.columns else 'Total'
                            if 'Country' in df.columns:
                                grp = df.groupby('Country', as_index=False)[val_col].sum().nlargest(10, val_col)
                                fig = px.bar(grp, x='Country', y=val_col, title='按国家汇总的销售额（前10）')
                                plot_html = fig.to_html(full_html=False)
                        elif numeric_cols:
                            # plot first numeric column distribution
                            col = numeric_cols[0]
                            fig = px.histogram(df, x=col, title=f'Distribution of {col}')
                            plot_html = fig.to_html(full_html=False)
                    except Exception as e:
                        error = str(e)

            # 尝试使用重试SQL
            last_sql = getattr(vn, 'last_sql', None)
            if not error and last_sql:
                sql2 = last_sql
                if sql2 and sql2.strip().lower().startswith('select'):
                    if not is_safe_sql(sql2):
                        error = 'Retry 生成的 SQL 包含不允许操作，已拒绝。'
                    else:
                        try:
                            df2 = pd.read_sql_query(sql2, conn)
                            result_html = df2.to_html(index=False)
                            sql = sql2
                            error = None
                            # try to generate plot for retry result as well
                            numeric_cols2 = [c for c in df2.columns if pd.api.types.is_numeric_dtype(df2[c])]
                            if 'TotalSales' in df2.columns or 'Total' in df2.columns:
                                val_col = 'TotalSales' if 'TotalSales' in df2.columns else 'Total'
                                if 'Country' in df2.columns:
                                    grp = df2.groupby('Country', as_index=False)[val_col].sum().nlargest(10, val_col)
                                    fig = px.bar(grp, x='Country', y=val_col, title='按国家汇总的销售额（前10）')
                                    plot_html = fig.to_html(full_html=False)
                            elif numeric_cols2:
                                col = numeric_cols2[0]
                                fig = px.histogram(df2, x=col, title=f'Distribution of {col}')
                                plot_html = fig.to_html(full_html=False)
                        except Exception as e2:
                            error = f"Retry failed: {e2}"

        if emb is None:
            emb_preview = None
        else:
            emb_preview = {
                'len': len(emb),
                'first8': emb[:8]
            }
        return render_template('result.html', question=question, sql=sql, result_html=result_html, error=error, emb=emb_preview, plot_html=plot_html)
    return render_template('index.html')


if __name__ == '__main__':
    # Start the Flask app
    flask_app.run(host='127.0.0.1', port=8084, debug=True)
