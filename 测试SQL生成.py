#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SQL生成功能
"""

import os
import sys
import traceback
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🧪 测试SQL生成功能")
print("=" * 60)

try:
    # 检查环境变量
    api_key = os.environ.get('ECOVAI_API_KEY')
    base_url = os.environ.get('ECOVAI_BASE_URL')
    text_model = os.environ.get('ECOVAI_TEXT_MODEL')
    embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

    print(f"✅ 环境变量检查完成")
    
    # 导入依赖
    from openai import OpenAI
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    from chromadb import Documents, EmbeddingFunction, Embeddings
    import pandas as pd
    import sqlite3
    
    print(f"✅ 依赖导入完成")
    
    # 创建客户端
    client = OpenAI(api_key=api_key, base_url=base_url)
    
    # 自定义Embedding函数
    class EcovaiEmbeddingFunction(EmbeddingFunction):
        def __init__(self, client, model):
            self.client = client
            self.model = model
        
        def __call__(self, input: Documents) -> Embeddings:
            embeddings = []
            for text in input:
                try:
                    resp = self.client.embeddings.create(model=self.model, input=text)
                    embeddings.append(resp.data[0].embedding)
                except Exception as e:
                    print(f"生成embedding时出错: {e}")
                    embeddings.append([0.0] * 1536)
            return embeddings
    
    # 创建RAG Vanna类
    class ECOVaiVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, client=None, config=None):
            embedding_function = EcovaiEmbeddingFunction(client, embedding_model)
            chroma_config = {
                "path": "./chroma_db",
                "embedding_function": embedding_function,
                "n_results": 5
            }
            ChromaDB_VectorStore.__init__(self, config=chroma_config)
            OpenAI_Chat.__init__(self, client=client, config=config)
            self.client = client
    
    # 实例化Vanna
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    print(f"✅ Vanna实例创建完成")
    
    # 连接到Chinook数据库
    chinook_path = Path('.') / 'Chinook.sqlite'
    if chinook_path.exists():
        vn.connect_to_sqlite(str(chinook_path))
        conn = sqlite3.connect(str(chinook_path), check_same_thread=False)
        print(f"✅ 数据库连接完成")
    else:
        print(f"❌ 数据库文件不存在: {chinook_path}")
        sys.exit(1)
    
    # 测试问题
    test_questions = [
        "每个国家的年度总销售额是多少？",
        "销售额最高的前5位艺术家是谁？",
        "哪个国家的客户最多？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试 {i}: {question}")
        print("-" * 40)
        
        try:
            # 使用Vanna的generate_sql方法
            sql = vn.generate_sql(question)
            print(f"📝 生成的SQL:")
            print(sql)
            
            # 执行SQL
            if sql and sql.strip().lower().startswith('select'):
                try:
                    df = pd.read_sql_query(sql, conn)
                    print(f"✅ SQL执行成功！返回 {len(df)} 行结果")
                    if len(df) > 0:
                        print("📊 示例结果（前3行）:")
                        print(df.head(3).to_string(index=False))
                except Exception as e:
                    print(f"❌ SQL执行失败: {e}")
            else:
                print(f"⚠️ 生成的SQL无效或不是SELECT查询")
                
        except Exception as e:
            print(f"❌ 生成SQL失败: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎉 测试完成！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    traceback.print_exc()
    sys.exit(1)
