@echo off
chcp 65001
echo ============================================================
echo 🚀 Vanna + Ecovai SQL生成器启动脚本
echo ============================================================

echo 📁 当前目录: %CD%
echo 🐍 激活虚拟环境...

call .venv\Scripts\activate.bat

echo ✅ 虚拟环境已激活
echo 🔧 检查依赖...

python -c "import sys; print('Python版本:', sys.version)"

echo 🚀 启动网页演示...
echo 💡 启动后请访问: http://localhost:8084
echo ⚠️  按 Ctrl+C 停止服务器
echo ============================================================

python web_demo_ecovai.py

pause
