# 简单的Vanna + Ecovai演示脚本
# 通过命令行交互方式演示SQL生成功能

import os
from dotenv import load_dotenv
load_dotenv()

from openai import OpenAI
import pandas as pd
import sqlite3
from pathlib import Path
import re

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.openai.openai_embeddings import OpenAI_Embeddings

# 构建Ecovai客户端
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

print("🔧 正在初始化Ecovai客户端...")
client = OpenAI(api_key=api_key, base_url=base_url)

# 创建ECOVai Vanna类
class ECOVaiVanna(OpenAI_Chat, OpenAI_Embeddings):
    def __init__(self, client=None, config=None):
        OpenAI_Chat.__init__(self, client=client, config=config)
        OpenAI_Embeddings.__init__(self, client=client, config=config)

    def generate_embedding(self, data: str, **kwargs):
        """使用Ecovai的embedding模型生成向量"""
        try:
            resp = self.client.embeddings.create(model=embedding_model, input=data)
            return resp.data[0].embedding
        except Exception as e:
            print(f"❌ 生成embedding时出错: {e}")
            return None

    # 实现必需的抽象方法（简化版本）
    def get_similar_question_sql(self, question: str, **kwargs):
        return []

    def get_related_ddl(self, question: str, **kwargs):
        return []

    def get_related_documentation(self, question: str, **kwargs):
        return []

    def add_question_sql(self, question: str, sql: str, **kwargs):
        return 'added'

    def add_ddl(self, ddl: str, **kwargs):
        return 'added'

    def add_documentation(self, documentation: str, **kwargs):
        return 'added'

    def get_training_data(self, **kwargs):
        return pd.DataFrame()

    def remove_training_data(self, id: str, **kwargs):
        return True

def extract_sql(text: str) -> str:
    """从AI回复中提取SQL代码"""
    if not text:
        return ''
    
    # 查找 ```sql ... ``` 代码块
    m = re.search(r"```sql\s*(.*?)```", text, re.S | re.I)
    if m:
        return m.group(1).strip()
    
    # 查找任何 ``` ... ``` 代码块
    m = re.search(r"```\s*(.*?)```", text, re.S)
    if m:
        return m.group(1).strip()
    
    # 查找SELECT语句
    m = re.search(r"(SELECT[\s\S]*?;)", text, re.I)
    if m:
        return m.group(1).strip()
    
    return text.strip()

def main():
    print("=" * 60)
    print("🚀 Vanna + Ecovai SQL生成器演示")
    print("=" * 60)
    
    # 实例化Vanna
    print("🤖 正在初始化AI模型...")
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    
    # 连接到Chinook数据库
    chinook_path = Path('.') / 'Chinook.sqlite'
    if not chinook_path.exists():
        import urllib.request
        print('📥 正在下载Chinook.sqlite数据库...')
        urllib.request.urlretrieve('https://vanna.ai/Chinook.sqlite', str(chinook_path))
    
    print("🗄️ 正在连接数据库...")
    conn = sqlite3.connect(str(chinook_path))
    
    print("✅ 初始化完成！")
    print("=" * 60)
    print("📊 数据库: Chinook (音乐商店数据)")
    print("🤖 AI模型: Ecovai " + text_model)
    print("=" * 60)
    print("💡 建议问题:")
    print("   1. 销售额最高的前5位艺术家是谁？")
    print("   2. 每个国家的总销售额是多少？")
    print("   3. 哪个流派最受欢迎？")
    print("   4. 最贵的专辑有哪些？")
    print("   5. 输入 'quit' 退出程序")
    print("=" * 60)
    
    while True:
        try:
            # 获取用户问题
            question = input("\n🤔 请输入你的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
            
            if not question:
                continue
            
            print(f"\n📝 问题: {question}")
            print("🔄 正在生成SQL...")
            
            # 构建提示
            prompt = [
                vn.system_message('你是一个SQL生成助手。请根据用户问题生成对应的SQL查询。数据库是Chinook音乐商店数据库。'),
                vn.user_message(question)
            ]
            
            # 调用AI生成SQL
            raw_response = vn.submit_prompt(prompt, model=text_model)
            sql = extract_sql(raw_response)
            
            print(f"🔍 生成的SQL:")
            print("-" * 40)
            print(sql)
            print("-" * 40)
            
            # 执行SQL
            if sql and sql.strip().lower().startswith('select'):
                try:
                    print("⚡ 正在执行查询...")
                    df = pd.read_sql_query(sql, conn)
                    
                    print(f"📊 查询结果 (共{len(df)}行):")
                    print("=" * 50)
                    if len(df) > 0:
                        # 显示前10行
                        print(df.head(10).to_string(index=False))
                        if len(df) > 10:
                            print(f"... (还有{len(df)-10}行)")
                    else:
                        print("没有找到匹配的数据")
                    print("=" * 50)
                    
                except Exception as e:
                    print(f"❌ 执行SQL时出错: {e}")
            else:
                print("⚠️ 生成的不是有效的SELECT查询")
            
            # 生成embedding演示
            print("\n🧠 正在生成问题的向量表示...")
            embedding = vn.generate_embedding(question)
            if embedding:
                print(f"✅ 向量维度: {len(embedding)}")
                print(f"📈 向量前5个值: {embedding[:5]}")
            
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
    
    conn.close()

if __name__ == '__main__':
    main()
