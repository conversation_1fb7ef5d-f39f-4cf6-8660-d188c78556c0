import sqlite3
from pathlib import Path

# 连接到Chinook数据库
chinook_path = Path('.') / 'Chinook.sqlite'
if chinook_path.exists():
    conn = sqlite3.connect(str(chinook_path))
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print("🗄️ Chinook数据库中的表:")
    for table in tables:
        print(f"  - {table[0]}")
    
    print("\n📊 主要表的结构:")
    
    # 检查Invoice表
    print("\n🧾 Invoice表结构:")
    cursor.execute("PRAGMA table_info(Invoice)")
    invoice_cols = cursor.fetchall()
    for col in invoice_cols:
        print(f"  {col[1]} ({col[2]})")
    
    # 检查Customer表
    print("\n👤 Customer表结构:")
    cursor.execute("PRAGMA table_info(Customer)")
    customer_cols = cursor.fetchall()
    for col in customer_cols:
        print(f"  {col[1]} ({col[2]})")
    
    # 检查InvoiceLine表
    print("\n📋 InvoiceLine表结构:")
    cursor.execute("PRAGMA table_info(InvoiceLine)")
    invoiceline_cols = cursor.fetchall()
    for col in invoiceline_cols:
        print(f"  {col[1]} ({col[2]})")
    
    # 示例查询：每个国家的年度总销售额
    print("\n💡 正确的SQL查询示例:")
    print("问题：每个国家的年度总销售额是多少？")
    
    correct_sql = """
SELECT 
    c.Country,
    strftime('%Y', i.InvoiceDate) AS Year,
    SUM(i.Total) AS TotalSales
FROM 
    Invoice i
    JOIN Customer c ON i.CustomerId = c.CustomerId
GROUP BY 
    c.Country, 
    strftime('%Y', i.InvoiceDate)
ORDER BY 
    c.Country, 
    Year;
"""
    
    print("正确的SQL:")
    print(correct_sql)
    
    # 执行查询验证
    try:
        cursor.execute(correct_sql.strip())
        results = cursor.fetchmany(10)
        print("\n✅ 查询结果示例（前10行）:")
        for row in results:
            print(f"  {row}")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    conn.close()
else:
    print("❌ Chinook.sqlite文件不存在")
