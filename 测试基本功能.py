#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Vanna + Ecovai基本功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from dotenv import load_dotenv
    load_dotenv()
    
    from openai import OpenAI
    import pandas as pd
    import sqlite3
    from pathlib import Path
    
    print("✅ 基本库导入成功")
    
    # 测试Ecovai连接
    api_key = os.environ.get('ECOVAI_API_KEY')
    base_url = os.environ.get('ECOVAI_BASE_URL')
    text_model = os.environ.get('ECOVAI_TEXT_MODEL')
    embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')
    
    print(f"🔑 API Key: {api_key[:10]}..." if api_key else "❌ 未找到API Key")
    print(f"🌐 Base URL: {base_url}")
    print(f"🤖 Text Model: {text_model}")
    print(f"📊 Embedding Model: {embedding_model}")
    
    # 测试OpenAI客户端
    client = OpenAI(api_key=api_key, base_url=base_url)
    print("✅ OpenAI客户端创建成功")
    
    # 测试数据库
    chinook_path = Path('.') / 'Chinook.sqlite'
    if chinook_path.exists():
        print("✅ Chinook数据库文件存在")
        conn = sqlite3.connect(str(chinook_path))
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 数据库包含 {len(tables)} 个表: {[t[0] for t in tables[:5]]}")
        conn.close()
    else:
        print("❌ Chinook数据库文件不存在，需要下载")
    
    # 测试Vanna导入
    try:
        from vanna.openai.openai_chat import OpenAI_Chat
        from vanna.openai.openai_embeddings import OpenAI_Embeddings
        print("✅ Vanna模块导入成功")
    except ImportError as e:
        print(f"❌ Vanna模块导入失败: {e}")
    
    # 测试Flask导入
    try:
        from vanna.flask import VannaFlaskApp
        print("✅ Vanna Flask模块导入成功")
    except ImportError as e:
        print(f"❌ Vanna Flask模块导入失败: {e}")
    
    print("\n🎉 基本功能测试完成！")
    
except Exception as e:
    print(f"❌ 测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
