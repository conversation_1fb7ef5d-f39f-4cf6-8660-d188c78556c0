#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试版本的初始化脚本
"""

import os
import sys
import traceback
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=" * 80)
print("🔧 调试初始化RAG知识库")
print("=" * 80)

# 检查环境变量
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

print(f"API Key: {'✅' if api_key else '❌'}")
print(f"Base URL: {base_url}")
print(f"Text Model: {text_model}")
print(f"Embedding Model: {embedding_model}")

if not all([api_key, base_url, text_model, embedding_model]):
    print("❌ 环境变量配置不完整")
    sys.exit(1)

try:
    print("\n📦 导入依赖...")
    from openai import OpenAI
    print("✅ OpenAI导入成功")
    
    from vanna.openai.openai_chat import OpenAI_Chat
    print("✅ Vanna OpenAI Chat导入成功")
    
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    print("✅ Vanna ChromaDB导入成功")
    
    client = OpenAI(api_key=api_key, base_url=base_url)
    print("✅ OpenAI客户端创建成功")
    
    # 测试API连接
    print("\n🌐 测试API连接...")
    response = client.chat.completions.create(
        model=text_model,
        messages=[{"role": "user", "content": "Hello"}],
        max_tokens=5
    )
    print(f"✅ Chat API正常: {response.choices[0].message.content}")
    
    resp = client.embeddings.create(model=embedding_model, input="test")
    print(f"✅ Embedding API正常，维度: {len(resp.data[0].embedding)}")
    
    # 自定义Embedding函数
    print("\n🔧 创建自定义Embedding函数...")
    class EcovaiEmbeddingFunction:
        def __init__(self, client, model):
            self.client = client
            self.model = model
            self._name = f"ecovai-{model}"
        
        def name(self):
            """ChromaDB要求的name方法"""
            return self._name
        
        def __call__(self, input_texts):
            """ChromaDB要求的embedding函数接口"""
            print(f"  生成embedding for {len(input_texts)} texts...")
            embeddings = []
            for text in input_texts:
                try:
                    resp = self.client.embeddings.create(model=self.model, input=text)
                    embeddings.append(resp.data[0].embedding)
                except Exception as e:
                    print(f"  ❌ 生成embedding时出错: {e}")
                    # 返回零向量作为fallback
                    embeddings.append([0.0] * 1536)  # 假设1536维
            print(f"  ✅ 生成了 {len(embeddings)} 个embeddings")
            return embeddings
    
    embedding_function = EcovaiEmbeddingFunction(client, embedding_model)
    print(f"✅ Embedding函数创建成功，名称: {embedding_function.name()}")
    
    # 创建RAG Vanna类
    print("\n🏗️ 创建RAG Vanna类...")
    class ECOVaiVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, client=None, config=None):
            # 配置ChromaDB
            chroma_config = {
                "path": "./chroma_db",  # 持久化存储路径
                "embedding_function": embedding_function,
                "n_results": 5  # 检索结果数量
            }
            
            print(f"  ChromaDB配置: {chroma_config}")
            
            # 初始化父类
            print("  初始化ChromaDB_VectorStore...")
            ChromaDB_VectorStore.__init__(self, config=chroma_config)
            print("  ✅ ChromaDB_VectorStore初始化成功")
            
            print("  初始化OpenAI_Chat...")
            OpenAI_Chat.__init__(self, client=client, config=config)
            print("  ✅ OpenAI_Chat初始化成功")
            
            self.client = client
    
    print("✅ RAG Vanna类定义成功")
    
    # 创建实例
    print("\n🚀 创建Vanna实例...")
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    print("✅ Vanna实例创建成功")
    
    # 测试添加数据
    print("\n📝 测试添加数据...")
    
    # 添加DDL
    ddl = "CREATE TABLE Customer (CustomerId INTEGER, Country NVARCHAR(40));"
    print(f"  添加DDL: {ddl}")
    result = vn.add_ddl(ddl)
    print(f"  ✅ DDL添加结果: {result}")
    
    # 添加问答对
    question = "每个国家有多少客户？"
    sql = "SELECT Country, COUNT(*) FROM Customer GROUP BY Country;"
    print(f"  添加问答: {question} -> {sql}")
    result = vn.add_question_sql(question, sql)
    print(f"  ✅ 问答添加结果: {result}")
    
    # 添加文档
    doc = "Customer表包含客户信息，Country字段表示客户所在国家"
    print(f"  添加文档: {doc}")
    result = vn.add_documentation(doc)
    print(f"  ✅ 文档添加结果: {result}")
    
    # 测试检索
    print("\n🔍 测试检索功能...")
    test_question = "哪个国家的客户最多？"
    
    similar = vn.get_similar_question_sql(test_question)
    print(f"  相似问题: {similar}")
    
    ddl_list = vn.get_related_ddl(test_question)
    print(f"  相关DDL: {ddl_list}")
    
    docs = vn.get_related_documentation(test_question)
    print(f"  相关文档: {docs}")
    
    print("\n🎉 调试初始化完成！")
    
except Exception as e:
    print(f"\n❌ 发生错误: {e}")
    print("\n📋 详细错误信息:")
    traceback.print_exc()
    sys.exit(1)
