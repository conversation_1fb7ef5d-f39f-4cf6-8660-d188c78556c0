#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动修复后的网页演示
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=" * 80)
print("🚀 启动修复后的Vanna + Ecovai网页演示")
print("=" * 80)

# 检查环境
print("🔧 检查环境...")

# 检查.env文件
env_path = Path('.env')
if env_path.exists():
    print("✅ .env配置文件存在")
else:
    print("❌ .env配置文件不存在")

# 检查数据库
db_path = Path('Chinook.sqlite')
if db_path.exists():
    print("✅ Chinook.sqlite数据库存在")
else:
    print("⚠️ Chinook.sqlite数据库不存在，将自动下载")

# 检查模板文件
templates_path = Path('templates')
if templates_path.exists():
    print("✅ templates目录存在")
    if (templates_path / 'index.html').exists():
        print("✅ index.html模板存在")
    if (templates_path / 'result.html').exists():
        print("✅ result.html模板存在")
else:
    print("❌ templates目录不存在")

print("\n🌐 启动信息:")
print("📍 访问地址: http://127.0.0.1:8084")
print("🛑 停止服务: 按 Ctrl+C")
print("=" * 80)

# 启动应用
try:
    import web_demo_ecovai
    print("✅ 应用模块加载成功")
except Exception as e:
    print(f"❌ 应用模块加载失败: {e}")
    sys.exit(1)
