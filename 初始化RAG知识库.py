#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
初始化Vanna RAG知识库
正确实现向量存储和检索
"""

import os
import sys
import sqlite3
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from openai import OpenAI
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# 配置
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

client = OpenAI(api_key=api_key, base_url=base_url)

# 自定义Embedding函数
from chromadb import Documents, EmbeddingFunction, Embeddings

class EcovaiEmbeddingFunction(EmbeddingFunction):
    def __init__(self, client, model):
        self.client = client
        self.model = model

    def __call__(self, input: Documents) -> Embeddings:
        """ChromaDB要求的embedding函数接口"""
        embeddings = []
        for text in input:
            try:
                resp = self.client.embeddings.create(model=self.model, input=text)
                embeddings.append(resp.data[0].embedding)
            except Exception as e:
                print(f"生成embedding时出错: {e}")
                # 返回零向量作为fallback
                embeddings.append([0.0] * 1536)  # 假设1536维
        return embeddings

# 创建RAG Vanna类
class ECOVaiVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, client=None, config=None):
        # 创建自定义embedding函数
        embedding_function = EcovaiEmbeddingFunction(client, embedding_model)
        
        # 配置ChromaDB
        chroma_config = {
            "path": "./chroma_db",  # 持久化存储路径
            "embedding_function": embedding_function,
            "n_results": 5  # 检索结果数量
        }
        
        # 初始化父类
        ChromaDB_VectorStore.__init__(self, config=chroma_config)
        OpenAI_Chat.__init__(self, client=client, config=config)
        
        self.client = client

def initialize_knowledge_base():
    """初始化RAG知识库"""
    print("=" * 80)
    print("🚀 初始化Vanna RAG知识库")
    print("=" * 80)
    
    # 创建Vanna实例
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    
    print("📊 1. 添加数据库结构信息（DDL）...")
    
    # 添加数据库结构
    ddl_statements = [
        "CREATE TABLE Artist (ArtistId INTEGER PRIMARY KEY, Name NVARCHAR(120) NOT NULL);",
        "CREATE TABLE Album (AlbumId INTEGER PRIMARY KEY, Title NVARCHAR(160) NOT NULL, ArtistId INTEGER NOT NULL, FOREIGN KEY (ArtistId) REFERENCES Artist(ArtistId));",
        "CREATE TABLE Track (TrackId INTEGER PRIMARY KEY, Name NVARCHAR(200) NOT NULL, AlbumId INTEGER, Composer NVARCHAR(220), Milliseconds INTEGER, Bytes INTEGER, UnitPrice NUMERIC(10,2), FOREIGN KEY (AlbumId) REFERENCES Album(AlbumId));",
        "CREATE TABLE Customer (CustomerId INTEGER PRIMARY KEY, FirstName NVARCHAR(40) NOT NULL, LastName NVARCHAR(20) NOT NULL, Company NVARCHAR(80), Address NVARCHAR(70), City NVARCHAR(40), State NVARCHAR(40), Country NVARCHAR(40), PostalCode NVARCHAR(10), Phone NVARCHAR(24), Fax NVARCHAR(24), Email NVARCHAR(60) NOT NULL);",
        "CREATE TABLE Invoice (InvoiceId INTEGER PRIMARY KEY, CustomerId INTEGER NOT NULL, InvoiceDate DATETIME NOT NULL, BillingAddress NVARCHAR(70), BillingCity NVARCHAR(40), BillingState NVARCHAR(40), BillingCountry NVARCHAR(40), BillingPostalCode NVARCHAR(10), Total NUMERIC(10,2) NOT NULL, FOREIGN KEY (CustomerId) REFERENCES Customer(CustomerId));",
        "CREATE TABLE InvoiceLine (InvoiceLineId INTEGER PRIMARY KEY, InvoiceId INTEGER NOT NULL, TrackId INTEGER NOT NULL, UnitPrice NUMERIC(10,2) NOT NULL, Quantity INTEGER NOT NULL, FOREIGN KEY (InvoiceId) REFERENCES Invoice(InvoiceId), FOREIGN KEY (TrackId) REFERENCES Track(TrackId));",
        "CREATE TABLE Genre (GenreId INTEGER PRIMARY KEY, Name NVARCHAR(120));",
        "CREATE TABLE MediaType (MediaTypeId INTEGER PRIMARY KEY, Name NVARCHAR(120));"
    ]
    
    for ddl in ddl_statements:
        result = vn.add_ddl(ddl)
        print(f"✅ 添加DDL: {ddl[:50]}... -> {result}")
    
    print("\n💬 2. 添加问答示例...")
    
    # 添加问答示例
    question_sql_pairs = [
        {
            "question": "每个国家的总销售额是多少？",
            "sql": "SELECT c.Country, SUM(i.Total) AS TotalSales FROM Invoice i JOIN Customer c ON i.CustomerId = c.CustomerId GROUP BY c.Country ORDER BY TotalSales DESC;"
        },
        {
            "question": "每个国家的年度总销售额是多少？",
            "sql": "SELECT c.Country, strftime('%Y', i.InvoiceDate) AS Year, SUM(i.Total) AS TotalSales FROM Invoice i JOIN Customer c ON i.CustomerId = c.CustomerId GROUP BY c.Country, strftime('%Y', i.InvoiceDate) ORDER BY c.Country, Year;"
        },
        {
            "question": "销售额最高的前5位艺术家是谁？",
            "sql": "SELECT a.Name, SUM(il.UnitPrice * il.Quantity) as TotalSales FROM Artist a JOIN Album al ON a.ArtistId = al.ArtistId JOIN Track t ON al.AlbumId = t.AlbumId JOIN InvoiceLine il ON t.TrackId = il.TrackId GROUP BY a.ArtistId, a.Name ORDER BY TotalSales DESC LIMIT 5;"
        },
        {
            "question": "哪个国家的客户最多？",
            "sql": "SELECT Country, COUNT(*) as CustomerCount FROM Customer GROUP BY Country ORDER BY CustomerCount DESC LIMIT 1;"
        },
        {
            "question": "2012年的总销售额是多少？",
            "sql": "SELECT SUM(Total) as TotalSales FROM Invoice WHERE strftime('%Y', InvoiceDate) = '2012';"
        },
        {
            "question": "最受欢迎的音乐流派是什么？",
            "sql": "SELECT g.Name, COUNT(il.InvoiceLineId) as PurchaseCount FROM Genre g JOIN Track t ON g.GenreId = t.GenreId JOIN InvoiceLine il ON t.TrackId = il.TrackId GROUP BY g.GenreId, g.Name ORDER BY PurchaseCount DESC LIMIT 1;"
        },
        {
            "question": "平均订单金额是多少？",
            "sql": "SELECT AVG(Total) as AverageOrderValue FROM Invoice;"
        },
        {
            "question": "销售额最高的专辑是什么？",
            "sql": "SELECT al.Title, SUM(il.UnitPrice * il.Quantity) as AlbumSales FROM Album al JOIN Track t ON al.AlbumId = t.AlbumId JOIN InvoiceLine il ON t.TrackId = il.TrackId GROUP BY al.AlbumId, al.Title ORDER BY AlbumSales DESC LIMIT 1;"
        }
    ]
    
    for pair in question_sql_pairs:
        result = vn.add_question_sql(pair["question"], pair["sql"])
        print(f"✅ 添加问答: {pair['question'][:30]}... -> {result}")
    
    print("\n📚 3. 添加业务文档...")
    
    # 添加业务文档
    documentation = [
        "Chinook数据库是一个音乐商店的示例数据库，包含艺术家、专辑、音轨、客户和销售数据。",
        "销售额计算：Invoice表中的Total字段包含每张发票的总金额，InvoiceLine表包含每个商品的单价和数量。",
        "客户信息：Customer表包含客户的个人信息，包括姓名、地址、国家等。",
        "音乐层级关系：Artist（艺术家）-> Album（专辑）-> Track（音轨），每个层级通过外键关联。",
        "销售关系：Customer -> Invoice -> InvoiceLine -> Track，表示客户购买音轨的完整流程。",
        "日期处理：InvoiceDate字段存储发票日期，可以使用strftime函数提取年份、月份等。",
        "流派信息：Genre表包含音乐流派信息，通过Track表与销售数据关联。",
        "媒体类型：MediaType表包含音频格式信息（如MP3、AAC等）。"
    ]
    
    for doc in documentation:
        result = vn.add_documentation(doc)
        print(f"✅ 添加文档: {doc[:40]}... -> {result}")
    
    print("\n🧪 4. 测试RAG检索功能...")
    
    # 测试检索功能
    test_question = "每个国家的年度总销售额是多少？"
    
    print(f"\n🔍 测试问题: {test_question}")
    
    # 测试相似问题检索
    similar_questions = vn.get_similar_question_sql(test_question)
    print(f"\n📋 检索到的相似问题 ({len(similar_questions)} 个):")
    for i, item in enumerate(similar_questions[:3], 1):
        if isinstance(item, dict):
            print(f"  {i}. {item.get('question', 'N/A')}")
        else:
            print(f"  {i}. {item}")
    
    # 测试相关DDL检索
    related_ddl = vn.get_related_ddl(test_question)
    print(f"\n🏗️ 检索到的相关DDL ({len(related_ddl)} 个):")
    for i, ddl in enumerate(related_ddl[:3], 1):
        print(f"  {i}. {ddl[:60]}...")
    
    # 测试相关文档检索
    related_docs = vn.get_related_documentation(test_question)
    print(f"\n📖 检索到的相关文档 ({len(related_docs)} 个):")
    for i, doc in enumerate(related_docs[:3], 1):
        print(f"  {i}. {doc[:60]}...")
    
    print(f"\n{'='*80}")
    print("🎉 RAG知识库初始化完成！")
    print("💡 现在可以运行 web_demo_ecovai.py 来测试智能SQL生成")
    print(f"{'='*80}")

if __name__ == '__main__':
    initialize_knowledge_base()
