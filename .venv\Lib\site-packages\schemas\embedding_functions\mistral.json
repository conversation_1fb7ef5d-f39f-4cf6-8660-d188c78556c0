{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Mistral Embedding Function Schema", "description": "Schema for the Mistral embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"model": {"type": "string", "description": "Parameter model for the Mistral embedding function"}, "api_key_env_var": {"type": "string", "description": "Parameter api_key_env_var for the Mistral embedding function"}}, "required": ["api_key_env_var", "model"], "additionalProperties": false}