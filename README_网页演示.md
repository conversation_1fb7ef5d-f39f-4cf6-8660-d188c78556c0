# Vanna + Ecovai 网页演示使用指南

## 📖 项目简介

这是一个基于Vanna框架和Ecovai API的智能SQL生成器网页演示。该项目可以让你通过自然语言提问，自动生成SQL查询并执行，返回可视化结果。

### 🎯 主要功能

1. **自然语言转SQL**: 输入中文或英文问题，自动生成对应的SQL查询
2. **智能数据分析**: 自动执行SQL并返回结果表格
3. **数据可视化**: 自动生成图表展示查询结果
4. **交互式界面**: 友好的网页界面，支持实时问答
5. **结果导出**: 支持CSV格式导出查询结果

### 🛠️ 技术架构

- **AI模型**: Ecovai DeepSeek-V3-0324 (文本生成) + text-embedding-ada-002 (向量化)
- **框架**: Vanna (RAG框架) + Flask (网页框架)
- **数据库**: SQLite (Chinook音乐商店示例数据)
- **前端**: HTML + JavaScript + CSS (内置)

## 🚀 快速开始

### 1. 环境准备

确保你已经安装了Python 3.9+和必要的依赖：

```bash
# 激活虚拟环境
.\.venv\Scripts\Activate.ps1

# 安装依赖（如果还没安装）
pip install vanna flask openai python-dotenv pandas sqlite3
```

### 2. 配置API密钥

项目已经配置好了Ecovai API，配置文件在`.env`：

```
ECOVAI_API_KEY=sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56
ECOVAI_BASE_URL=https://api.ecovai.cn/v1
ECOVAI_TEXT_MODEL=DeepSeek-V3-0324
ECOVAI_EMBEDDING_MODEL=text-embedding-ada-002
```

### 3. 启动网页演示

```bash
python web_demo_ecovai.py
```

启动后你会看到：
```
============================================================
🚀 Vanna + Ecovai SQL生成器启动中...
============================================================
📊 数据库: Chinook (音乐商店数据)
🤖 AI模型: Ecovai DeepSeek-V3-0324
🔗 访问地址: http://localhost:8084
============================================================
💡 你可以尝试问这些问题:
   - 销售额最高的前10位艺术家是谁？
   - 每个国家的年度总销售额是多少？
   - 哪个流派的销售额最高？
   - 最受欢迎的专辑有哪些？
============================================================
```

### 4. 访问网页界面

打开浏览器访问: **http://localhost:8084**

## 💡 使用示例

### 示例问题（中文）
- "销售额最高的前10位艺术家是谁？"
- "每个国家的年度总销售额是多少？"
- "哪个流派的销售额最高？"
- "最受欢迎的专辑有哪些？"
- "哪些客户购买最多？"

### 示例问题（英文）
- "What are the top 10 artists by sales?"
- "What are the total sales per year by country?"
- "Which genre has the highest sales?"
- "What are the most popular albums?"
- "Who are the top customers by purchases?"

## 📊 数据库说明

项目使用Chinook示例数据库，包含以下主要表：

- **Artist**: 艺术家信息
- **Album**: 专辑信息
- **Track**: 音轨信息
- **Customer**: 客户信息
- **Invoice**: 发票信息
- **InvoiceLine**: 发票明细
- **Genre**: 音乐流派
- **MediaType**: 媒体类型

## 🔧 工作原理

1. **用户输入**: 在网页界面输入自然语言问题
2. **AI理解**: Ecovai模型理解问题意图
3. **SQL生成**: 基于数据库结构生成对应SQL查询
4. **执行查询**: 在SQLite数据库中执行SQL
5. **结果展示**: 以表格和图表形式展示结果
6. **交互反馈**: 用户可以进一步提问或修改查询

## 🎨 界面功能

- **问题输入框**: 输入自然语言问题
- **建议问题**: 点击预设问题快速开始
- **SQL显示**: 查看生成的SQL代码
- **结果表格**: 查看查询结果数据
- **图表可视化**: 自动生成相关图表
- **CSV导出**: 下载查询结果
- **历史记录**: 查看之前的问答记录

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 确认API密钥正确
   - 检查Ecovai服务状态

2. **数据库连接失败**
   - 确保Chinook.sqlite文件存在
   - 检查文件权限

3. **网页无法访问**
   - 确认端口8084未被占用
   - 检查防火墙设置

### 调试模式

如果遇到问题，可以查看控制台输出的详细日志信息。

## 📝 扩展功能

你可以基于这个演示进行扩展：

1. **连接其他数据库**: 修改数据库连接配置
2. **添加更多AI模型**: 集成其他LLM提供商
3. **自定义界面**: 修改Flask模板和样式
4. **增加认证**: 添加用户登录功能
5. **部署到云端**: 部署到云服务器供团队使用

## 📞 技术支持

如果你在使用过程中遇到问题，可以：

1. 查看控制台错误信息
2. 检查API配置是否正确
3. 确认网络连接正常
4. 参考Vanna官方文档: https://vanna.ai/docs/

---

**享受你的AI驱动的SQL查询体验！** 🎉
