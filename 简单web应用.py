#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的Web应用，用于测试RAG功能
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from flask import Flask, render_template, request, jsonify
import sqlite3
import pandas as pd

app = Flask(__name__)

# 简单的HTML模板
INDEX_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanna + Ecovai SQL生成器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🚀 Vanna + Ecovai SQL生成器</h1>
    <p>使用RAG技术的智能SQL查询助手</p>
    
    <form method="POST">
        <div class="form-group">
            <label for="question">💬 请输入您的问题：</label>
            <input type="text" id="question" name="question" 
                   placeholder="例如：每个国家的年度总销售额是多少？" required>
        </div>
        <button type="submit">🎯 生成并执行SQL</button>
    </form>
    
    <div style="margin-top: 20px;">
        <h3>💡 建议问题：</h3>
        <button onclick="setQuestion('每个国家的年度总销售额是多少？')">每个国家的年度总销售额是多少？</button>
        <button onclick="setQuestion('销售额最高的前5位艺术家是谁？')">销售额最高的前5位艺术家是谁？</button>
        <button onclick="setQuestion('哪个国家的客户最多？')">哪个国家的客户最多？</button>
    </div>
    
    <script>
        function setQuestion(question) {
            document.getElementById('question').value = question;
        }
    </script>
</body>
</html>
"""

RESULT_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询结果 - Vanna + Ecovai SQL生成器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .result { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <h1>📊 查询结果</h1>
    
    <div class="result">
        <h3>🤔 您的问题</h3>
        <p><strong>{{ question }}</strong></p>
    </div>
    
    {% if sql %}
    <div class="result">
        <h3>🔍 生成的SQL查询</h3>
        <pre>{{ sql }}</pre>
    </div>
    {% endif %}
    
    {% if error %}
    <div class="result error">
        <h3>❌ 执行错误</h3>
        <p>{{ error }}</p>
    </div>
    {% endif %}
    
    {% if result_html %}
    <div class="result success">
        <h3>📋 查询结果</h3>
        {{ result_html | safe }}
    </div>
    {% endif %}
    
    <a href="/" class="btn">🔙 返回首页</a>
</body>
</html>
"""

# 初始化Vanna（简化版本，先测试基本功能）
vn = None
conn = None

def init_vanna():
    global vn, conn
    try:
        from openai import OpenAI
        from vanna.openai.openai_chat import OpenAI_Chat
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        from chromadb import Documents, EmbeddingFunction, Embeddings
        
        # 配置
        api_key = os.environ.get('ECOVAI_API_KEY')
        base_url = os.environ.get('ECOVAI_BASE_URL')
        text_model = os.environ.get('ECOVAI_TEXT_MODEL')
        embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')
        
        client = OpenAI(api_key=api_key, base_url=base_url)
        
        # 自定义Embedding函数
        class EcovaiEmbeddingFunction(EmbeddingFunction):
            def __init__(self, client, model):
                self.client = client
                self.model = model
            
            def __call__(self, input: Documents) -> Embeddings:
                embeddings = []
                for text in input:
                    try:
                        resp = self.client.embeddings.create(model=self.model, input=text)
                        embeddings.append(resp.data[0].embedding)
                    except Exception as e:
                        print(f"生成embedding时出错: {e}")
                        embeddings.append([0.0] * 1536)
                return embeddings
        
        # 创建RAG Vanna类
        class ECOVaiVanna(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, client=None, config=None):
                embedding_function = EcovaiEmbeddingFunction(client, embedding_model)
                chroma_config = {
                    "path": "./chroma_db",
                    "embedding_function": embedding_function,
                    "n_results": 5
                }
                ChromaDB_VectorStore.__init__(self, config=chroma_config)
                OpenAI_Chat.__init__(self, client=client, config=config)
                self.client = client
        
        # 实例化Vanna
        vn = ECOVaiVanna(client=client, config={'model': text_model})
        
        # 连接数据库
        chinook_path = Path('.') / 'Chinook.sqlite'
        if chinook_path.exists():
            vn.connect_to_sqlite(str(chinook_path))
            conn = sqlite3.connect(str(chinook_path), check_same_thread=False)
            print("✅ Vanna初始化成功")
            return True
        else:
            print("❌ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ Vanna初始化失败: {e}")
        return False

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        question = request.form.get('question')
        
        if not question:
            return render_template_string(INDEX_HTML)
        
        # 初始化Vanna（如果还没有初始化）
        if vn is None:
            if not init_vanna():
                return render_template_string(RESULT_HTML, 
                                            question=question, 
                                            sql=None, 
                                            result_html=None, 
                                            error="Vanna初始化失败")
        
        try:
            # 使用Vanna生成SQL
            sql = vn.generate_sql(question)
            
            # 执行SQL
            if sql and sql.strip().lower().startswith('select'):
                try:
                    df = pd.read_sql_query(sql, conn)
                    result_html = df.to_html(index=False, classes='table')
                    return render_template_string(RESULT_HTML, 
                                                question=question, 
                                                sql=sql, 
                                                result_html=result_html, 
                                                error=None)
                except Exception as e:
                    return render_template_string(RESULT_HTML, 
                                                question=question, 
                                                sql=sql, 
                                                result_html=None, 
                                                error=f"SQL执行失败: {e}")
            else:
                return render_template_string(RESULT_HTML, 
                                            question=question, 
                                            sql=sql, 
                                            result_html=None, 
                                            error="生成的SQL无效或不是SELECT查询")
                
        except Exception as e:
            return render_template_string(RESULT_HTML, 
                                        question=question, 
                                        sql=None, 
                                        result_html=None, 
                                        error=f"生成SQL失败: {e}")
    
    return render_template_string(INDEX_HTML)

if __name__ == '__main__':
    print("🚀 启动简化版Vanna + Ecovai Web应用")
    print("📍 访问地址: http://127.0.0.1:8084")
    print("🛑 停止服务: 按 Ctrl+C")
    app.run(host='127.0.0.1', port=8084, debug=True)
