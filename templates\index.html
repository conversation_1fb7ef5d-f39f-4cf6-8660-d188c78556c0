<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanna + Ecovai SQL生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, textarea:focus {
            border-color: #667eea;
            outline: none;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .suggestions {
            margin: 20px 0;
        }
        .suggestion-btn {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #1976d2;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
        }
        .suggestion-btn:hover {
            background: #1976d2;
            color: white;
        }
        .info-box {
            background: #f0f8ff;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Vanna + Ecovai SQL生成器</h1>
        <p>使用Ecovai API的智能SQL查询助手</p>
    </div>

    <div class="container">
        <form method="POST">
            <div class="form-group">
                <label for="question">💬 请输入您的问题（中文或英文）：</label>
                <input type="text" id="question" name="question"
                       placeholder="例如：每个国家的年度总销售额是多少？"
                       required>
            </div>

            <div class="suggestions">
                <p><strong>💡 建议问题（点击快速填入）：</strong></p>
                <button type="button" class="suggestion-btn" onclick="setQuestion('每个国家的年度总销售额是多少？')">
                    每个国家的年度总销售额是多少？
                </button>
                <button type="button" class="suggestion-btn" onclick="setQuestion('销售额最高的前5位艺术家是谁？')">
                    销售额最高的前5位艺术家是谁？
                </button>
                <button type="button" class="suggestion-btn" onclick="setQuestion('哪个国家的客户最多？')">
                    哪个国家的客户最多？
                </button>
                <button type="button" class="suggestion-btn" onclick="setQuestion('最受欢迎的音乐流派是什么？')">
                    最受欢迎的音乐流派是什么？
                </button>
                <button type="button" class="suggestion-btn" onclick="setQuestion('2012年的总销售额是多少？')">
                    2012年的总销售额是多少？
                </button>
            </div>

            <div class="form-group">
                <label for="raw_sql">🔧 或者直接输入SQL（可选）：</label>
                <textarea id="raw_sql" name="raw_sql" rows="4"
                          placeholder="SELECT * FROM Customer LIMIT 10;"></textarea>
            </div>

            <button type="submit" class="btn">🎯 生成并执行SQL</button>
        </form>

        <div class="info-box">
            <h3>📊 数据库信息</h3>
            <p><strong>数据库：</strong>Chinook音乐商店数据库</p>
            <p><strong>主要表：</strong>Customer（客户）、Invoice（发票）、Artist（艺术家）、Album（专辑）、Track（音轨）</p>
            <p><strong>AI模型：</strong>Ecovai DeepSeek-V3-0324</p>
        </div>
    </div>

    <script>
        function setQuestion(question) {
            document.getElementById('question').value = question;
        }
    </script>
</body>
</html>