#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动Web应用
"""

import os
import sys
import traceback
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 启动Vanna + Ecovai Web应用")
print("=" * 60)

try:
    # 检查环境变量
    api_key = os.environ.get('ECOVAI_API_KEY')
    base_url = os.environ.get('ECOVAI_BASE_URL')
    text_model = os.environ.get('ECOVAI_TEXT_MODEL')
    embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

    print(f"✅ API配置检查完成")
    
    # 导入依赖
    from openai import OpenAI
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    from chromadb import Documents, EmbeddingFunction, Embeddings
    from flask import Flask, render_template, request
    import pandas as pd
    import sqlite3
    import plotly.express as px
    
    print(f"✅ 依赖导入完成")
    
    # 创建客户端
    client = OpenAI(api_key=api_key, base_url=base_url)
    
    # 自定义Embedding函数
    class EcovaiEmbeddingFunction(EmbeddingFunction):
        def __init__(self, client, model):
            self.client = client
            self.model = model
        
        def __call__(self, input: Documents) -> Embeddings:
            embeddings = []
            for text in input:
                try:
                    resp = self.client.embeddings.create(model=self.model, input=text)
                    embeddings.append(resp.data[0].embedding)
                except Exception as e:
                    print(f"生成embedding时出错: {e}")
                    embeddings.append([0.0] * 1536)
            return embeddings
    
    # 创建RAG Vanna类
    class ECOVaiVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, client=None, config=None):
            embedding_function = EcovaiEmbeddingFunction(client, embedding_model)
            chroma_config = {
                "path": "./chroma_db",
                "embedding_function": embedding_function,
                "n_results": 5
            }
            ChromaDB_VectorStore.__init__(self, config=chroma_config)
            OpenAI_Chat.__init__(self, client=client, config=config)
            self.client = client
    
    # 实例化Vanna
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    print(f"✅ Vanna实例创建完成")
    
    # 连接到Chinook数据库
    chinook_path = Path('.') / 'Chinook.sqlite'
    if not chinook_path.exists():
        import urllib.request
        print('正在下载Chinook.sqlite数据库...')
        urllib.request.urlretrieve('https://vanna.ai/Chinook.sqlite', str(chinook_path))
    
    vn.connect_to_sqlite(str(chinook_path))
    conn = sqlite3.connect(str(chinook_path), check_same_thread=False)
    print(f"✅ 数据库连接完成")
    
    # 创建Flask应用
    app = Flask(__name__)
    
    # 提取SQL的辅助函数
    def extract_sql(text: str) -> str:
        import re
        if not text:
            return ''
        # 查找 ```sql ... ``` 代码块
        m = re.search(r"```sql\s*(.*?)```", text, re.S | re.I)
        if m:
            return m.group(1).strip()
        # 查找任何 ``` ... ``` 代码块
        m = re.search(r"```\s*(.*?)```", text, re.S)
        if m:
            return m.group(1).strip()
        # 回退：查找第一个 SELECT ... ;
        m = re.search(r"(SELECT[\s\S]*?;)", text, re.I)
        if m:
            return m.group(1).strip()
        return text.strip()
    
    @app.route('/', methods=['GET', 'POST'])
    def index():
        if request.method == 'POST':
            question = request.form.get('question')
            
            if not question:
                return render_template('index.html')
            
            try:
                print(f"🔍 处理问题: {question}")
                
                # 使用Vanna的generate_sql方法
                sql = vn.generate_sql(question)
                print(f"📝 生成的SQL: {sql}")
                
                # 执行SQL
                if sql and sql.strip().lower().startswith('select'):
                    try:
                        df = pd.read_sql_query(sql, conn)
                        result_html = df.to_html(index=False, classes='table table-striped')
                        
                        # 简单的可视化
                        plot_html = None
                        if 'TotalSales' in df.columns and 'Country' in df.columns:
                            fig = px.bar(df.head(10), x='Country', y='TotalSales', 
                                       title='销售额前10的国家')
                            plot_html = fig.to_html(full_html=False)
                        
                        return render_template('result.html', 
                                             question=question, 
                                             sql=sql, 
                                             result_html=result_html,
                                             plot_html=plot_html,
                                             error=None)
                    except Exception as e:
                        return render_template('result.html', 
                                             question=question, 
                                             sql=sql, 
                                             result_html=None,
                                             plot_html=None,
                                             error=str(e))
                else:
                    return render_template('result.html', 
                                         question=question, 
                                         sql=sql, 
                                         result_html=None,
                                         plot_html=None,
                                         error="生成的SQL无效或不是SELECT查询")
                    
            except Exception as e:
                print(f"❌ 处理错误: {e}")
                traceback.print_exc()
                return render_template('result.html', 
                                     question=question, 
                                     sql=None, 
                                     result_html=None,
                                     plot_html=None,
                                     error=str(e))
        
        return render_template('index.html')
    
    print(f"🌐 启动Flask应用...")
    print(f"📍 访问地址: http://127.0.0.1:8084")
    print(f"🛑 停止服务: 按 Ctrl+C")
    print("=" * 60)
    
    # 启动应用
    app.run(host='127.0.0.1', port=8084, debug=True)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    traceback.print_exc()
    sys.exit(1)
