<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询结果 - Vanna + Ecovai SQL生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .question-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin-bottom: 20px;
        }
        .sql-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .sql-box pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .error-box {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin-bottom: 20px;
        }
        .error-box h3 {
            color: #d32f2f;
            margin-top: 0;
        }
        .result-box {
            margin-bottom: 20px;
        }
        .result-box table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .result-box th, .result-box td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .result-box th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .result-box tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .visualization {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 查询结果</h1>
        <p>Vanna + Ecovai SQL生成器</p>
    </div>

    <div class="container">
        <div class="question-box">
            <h2>🤔 您的问题</h2>
            <p><strong>{{ question }}</strong></p>
        </div>

        {% if sql %}
        <div class="sql-box">
            <h3>🔍 生成的SQL查询</h3>
            <pre>{{ sql }}</pre>
        </div>
        {% endif %}

        {% if error %}
        <div class="error-box">
            <h3>❌ 执行错误</h3>
            <pre>{{ error }}</pre>
            <p><strong>💡 提示：</strong>请检查SQL语法或尝试重新描述问题。</p>
        </div>
        {% endif %}

        {% if result_html %}
        <div class="result-box">
            <h3>📋 查询结果</h3>
            {{ result_html | safe }}
        </div>
        {% endif %}

        {% if plot_html %}
        <div class="visualization">
            <h3>📈 数据可视化</h3>
            {{ plot_html | safe }}
        </div>
        {% endif %}

        {% if emb %}
        <div class="container">
            <h3>🧠 向量嵌入信息</h3>
            <p><strong>向量长度：</strong>{{ emb.len }}</p>
            <p><strong>前8维：</strong>{{ emb.first8 }}</p>
        </div>
        {% endif %}

        <a href="/" class="btn">🔙 返回首页</a>
    </div>
</body>
</html>