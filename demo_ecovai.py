# Demo using ECOVAI for both text (chat) and embeddings via OpenAI-compatible client
# Usage: .\.venv\Scripts\Activate.ps1; python demo_ecovai.py

import os
from dotenv import load_dotenv
load_dotenv()

from openai import OpenAI
import pandas as pd
import sqlite3
from pathlib import Path

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.openai.openai_embeddings import OpenAI_Embeddings

# Build client
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

client = OpenAI(api_key=api_key, base_url=base_url)

# Minimal concrete class for chat (implements required abstract methods)
class ECOVaiVanna(OpenAI_Chat, OpenAI_Embeddings):
    def __init__(self, client=None, config=None):
        OpenAI_Chat.__init__(self, client=client, config=config)
        OpenAI_Embeddings.__init__(self, client=client, config=config)

    # Implement storage / retrieval stubs using simple in-memory lists
    def generate_embedding(self, data: str, **kwargs):
        # Use embeddings API with the model from env
        resp = self.client.embeddings.create(model=embedding_model, input=data)
        # resp may be a pydantic model or a dict-like object. Normalize to a plain list.
        try:
            return resp.data[0].embedding
        except Exception:
            try:
                return resp['data'][0]['embedding']
            except Exception:
                return None

    def get_similar_question_sql(self, question: str, **kwargs):
        return []

    def get_related_ddl(self, question: str, **kwargs):
        return []

    def get_related_documentation(self, question: str, **kwargs):
        return []

    def add_question_sql(self, question: str, sql: str, **kwargs):
        return 'stub'

    def add_ddl(self, ddl: str, **kwargs):
        return 'stub'

    def add_documentation(self, documentation: str, **kwargs):
        return 'stub'

    def get_training_data(self, **kwargs):
        return pd.DataFrame()

    def remove_training_data(self, id: str, **kwargs):
        return True

# Instantiate
vn = ECOVaiVanna(client=client, config={'model': text_model})

# Connect to local Chinook DB (download if not present)
chinook_path = Path('.') / 'Chinook.sqlite'
if not chinook_path.exists():
    import urllib.request
    print('Downloading Chinook.sqlite...')
    urllib.request.urlretrieve('https://vanna.ai/Chinook.sqlite', str(chinook_path))

conn = sqlite3.connect(str(chinook_path))

# Helper: introspect schema (tables and columns) and format a compact description
def introspect_schema(connection) -> str:
    cur = connection.cursor()
    tables = []
    for row in cur.execute("SELECT name FROM sqlite_master WHERE type='table';"):
        tables.append(row[0])
    schema_lines = []
    for t in tables:
        try:
            cols = [r[1] for r in cur.execute(f"PRAGMA table_info('{t}')")]
        except Exception:
            cols = []
        schema_lines.append(f"- {t}: {', '.join(cols)}")
    if not schema_lines:
        return 'No tables found in database.'
    return 'Database schema:\n' + '\n'.join(schema_lines)

db_schema_text = introspect_schema(conn)
print('\nDetected DB schema (short):\n', db_schema_text)

# Ask a question (示例中文问题)
question = '每个国家的年度总销售额是多少？'
print('Question:', question)

system_base = 'You are a helpful SQL generation assistant. Use the provided database schema and generate SQL that matches the schema. Only return the SQL query inside a single ```sql``` code block if possible.'
system_with_schema = system_base + '\n\n' + db_schema_text

prompt = [
    vn.system_message(system_with_schema),
    vn.user_message(question)
]

print('Sending to ECOVAI text model...')
raw = vn.submit_prompt(prompt, model=text_model)

# The model may return text wrapped in markdown/code fences and additional commentary.
# Try to extract the first sql code block or the first SELECT statement.
def extract_sql(text: str) -> str:
    import re
    if not text:
        return ''
    # find ```sql ... ``` blocks
    m = re.search(r"```sql\s*(.*?)```", text, re.S | re.I)
    if m:
        return m.group(1).strip()
    # find any ``` ... ``` block
    m = re.search(r"```\s*(.*?)```", text, re.S)
    if m:
        return m.group(1).strip()
    # fallback: find first SELECT ... ;
    m = re.search(r"(SELECT[\s\S]*?;)", text, re.I)
    if m:
        return m.group(1).strip()
    # otherwise return the raw text
    return text.strip()

sql = extract_sql(raw)
print('\nExtracted SQL:\n', sql)

def try_execute_sql(sql_text: str):
    try:
        df = pd.read_sql_query(sql_text, conn)
        print('\nQuery result (head):')
        print(df.head())
        return True
    except Exception as e:
        print('Error running SQL:', e)
        return e

# Run SQL if it looks like a SELECT
if sql and sql.strip().lower().startswith('select'):
    exec_result = try_execute_sql(sql)
    # If execution failed due to missing table, retry once by giving the error + schema back to the model
    if isinstance(exec_result, Exception) and 'no such table' in str(exec_result).lower():
        print('\nModel generated SQL referenced tables that do not exist. Asking model to regenerate using actual schema...')
        retry_prompt = [
            vn.system_message(system_with_schema),
            vn.user_message(question + "\n\nThe previous SQL failed with error: " + str(exec_result) + "\nPlease produce a corrected SQL that uses the available tables and columns.")
        ]
        raw2 = vn.submit_prompt(retry_prompt, model=text_model)
        sql2 = extract_sql(raw2)
        print('\nRetry Extracted SQL:\n', sql2)
        if sql2 and sql2.strip().lower().startswith('select'):
            try_execute_sql(sql2)

# Generate embedding for the question
print('\nGenerating embedding for the question via ECOVAI embedding model...')
emb_response = vn.generate_embedding(question)
# openai library returns a pydantic model for embeddings; try to handle both dict-like and object
if hasattr(emb_response, 'data'):
    # pydantic CreateEmbeddingResponse: data is a list with embedding under .embedding
    emb = None
    try:
        emb = emb_response.data[0].embedding
    except Exception:
        # fallback if nested as dict
        emb = emb_response.data[0].get('embedding') if isinstance(emb_response.data[0], dict) else None
else:
    # assume dict-like
    emb = emb_response.get('data')[0]['embedding'] if isinstance(emb_response, dict) else None

if emb is None:
    print('Could not parse embedding response object; raw response:')
    print(emb_response)
else:
    print('Embedding length:', len(emb))
    print('First 8 dims:', emb[:8])

print('\nDemo complete.')
