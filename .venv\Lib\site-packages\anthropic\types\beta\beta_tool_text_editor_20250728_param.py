# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Optional
from typing_extensions import Literal, Required, TypedDict

from .beta_cache_control_ephemeral_param import BetaCacheControlEphemeralParam

__all__ = ["BetaToolTextEditor20250728Param"]


class BetaToolTextEditor20250728Param(TypedDict, total=False):
    name: Required[Literal["str_replace_based_edit_tool"]]
    """Name of the tool.

    This is how the tool will be called by the model and in `tool_use` blocks.
    """

    type: Required[Literal["text_editor_20250728"]]

    cache_control: Optional[BetaCacheControlEphemeralParam]
    """Create a cache control breakpoint at this content block."""

    max_characters: Optional[int]
    """Maximum number of characters to display when viewing a file.

    If not specified, defaults to displaying the full file.
    """
