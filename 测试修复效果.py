#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的Vanna + Ecovai功能
"""

import os
import sys
from dotenv import load_dotenv
load_dotenv()

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from openai import OpenAI
import pandas as pd
import sqlite3
from pathlib import Path

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.openai.openai_embeddings import OpenAI_Embeddings

# 构建Ecovai客户端
api_key = os.environ.get('ECOVAI_API_KEY')
base_url = os.environ.get('ECOVAI_BASE_URL')
text_model = os.environ.get('ECOVAI_TEXT_MODEL')
embedding_model = os.environ.get('ECOVAI_EMBEDDING_MODEL')

client = OpenAI(api_key=api_key, base_url=base_url)

# 使用修复后的ECOVai Vanna类
class ECOVaiVanna(OpenAI_Chat, OpenAI_Embeddings):
    def __init__(self, client=None, config=None):
        OpenAI_Chat.__init__(self, client=client, config=config)
        OpenAI_Embeddings.__init__(self, client=client, config=config)

    def generate_embedding(self, data: str, **kwargs):
        """使用Ecovai的embedding模型生成向量"""
        try:
            resp = self.client.embeddings.create(model=embedding_model, input=data)
            return resp.data[0].embedding
        except Exception as e:
            print(f"生成embedding时出错: {e}")
            return None

    # 实现必需的抽象方法（增强版本）
    def get_similar_question_sql(self, question: str, **kwargs):
        # 返回一些示例问答对来帮助AI理解
        examples = [
            {
                "question": "每个国家的总销售额是多少？",
                "sql": "SELECT c.Country, SUM(i.Total) AS TotalSales FROM Invoice i JOIN Customer c ON i.CustomerId = c.CustomerId GROUP BY c.Country ORDER BY TotalSales DESC;"
            },
            {
                "question": "销售额最高的前5位艺术家是谁？", 
                "sql": "SELECT a.Name, SUM(il.UnitPrice * il.Quantity) as TotalSales FROM Artist a JOIN Album al ON a.ArtistId = al.ArtistId JOIN Track t ON al.AlbumId = t.AlbumId JOIN InvoiceLine il ON t.TrackId = il.TrackId GROUP BY a.ArtistId ORDER BY TotalSales DESC LIMIT 5;"
            }
        ]
        return examples

    def get_related_ddl(self, question: str, **kwargs):
        # 返回相关的数据库结构信息
        ddl_info = [
            "CREATE TABLE Customer (CustomerId INTEGER, FirstName NVARCHAR(40), LastName NVARCHAR(20), Country NVARCHAR(40), Email NVARCHAR(60));",
            "CREATE TABLE Invoice (InvoiceId INTEGER, CustomerId INTEGER, InvoiceDate DATETIME, Total NUMERIC(10,2));",
            "CREATE TABLE InvoiceLine (InvoiceLineId INTEGER, InvoiceId INTEGER, TrackId INTEGER, UnitPrice NUMERIC(10,2), Quantity INTEGER);",
            "CREATE TABLE Artist (ArtistId INTEGER, Name NVARCHAR(120));",
            "CREATE TABLE Album (AlbumId INTEGER, Title NVARCHAR(160), ArtistId INTEGER);",
            "CREATE TABLE Track (TrackId INTEGER, Name NVARCHAR(200), AlbumId INTEGER);"
        ]
        return ddl_info

    def get_related_documentation(self, question: str, **kwargs):
        # 返回业务规则和说明
        docs = [
            "销售额计算：Invoice表中的Total字段包含每张发票的总金额",
            "客户信息：Customer表包含客户的国家、姓名等信息",
            "发票关系：Invoice通过CustomerId与Customer关联",
            "音乐数据：Artist -> Album -> Track -> InvoiceLine 的层级关系",
            "日期格式：InvoiceDate字段存储发票日期，可用strftime函数提取年份"
        ]
        return docs

    def add_question_sql(self, question: str, sql: str, **kwargs):
        return 'added'

    def add_ddl(self, ddl: str, **kwargs):
        return 'added'

    def add_documentation(self, documentation: str, **kwargs):
        return 'added'

    def get_training_data(self, **kwargs):
        return pd.DataFrame()

    def remove_training_data(self, id: str, **kwargs):
        return True

def test_sql_generation():
    """测试SQL生成功能"""
    print("=" * 80)
    print("🧪 测试修复后的SQL生成功能")
    print("=" * 80)
    
    # 创建Vanna实例
    vn = ECOVaiVanna(client=client, config={'model': text_model})
    
    # 连接数据库
    chinook_path = Path('.') / 'Chinook.sqlite'
    if not chinook_path.exists():
        print("❌ Chinook.sqlite文件不存在")
        return
    
    conn = sqlite3.connect(str(chinook_path))
    
    # 测试问题
    test_questions = [
        "每个国家的年度总销售额是多少？",
        "销售额最高的前5位艺术家是谁？",
        "哪个国家的客户最多？",
        "2012年的总销售额是多少？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试 {i}: {question}")
        print("-" * 60)
        
        # 获取相关信息
        related_ddl = vn.get_related_ddl(question)
        similar_examples = vn.get_similar_question_sql(question)
        related_docs = vn.get_related_documentation(question)
        
        # 构建增强的系统消息
        system_message = """你是一个SQL生成专家。请根据以下信息生成准确的SQL查询：

数据库结构：
"""
        for ddl in related_ddl:
            system_message += f"{ddl}\n"
        
        system_message += "\n相似问题示例：\n"
        for example in similar_examples:
            system_message += f"问题: {example['question']}\nSQL: {example['sql']}\n\n"
        
        system_message += "业务规则：\n"
        for doc in related_docs:
            system_message += f"- {doc}\n"
        
        system_message += "\n请严格按照上述数据库结构生成SQL，只使用存在的表和字段。将SQL放在```sql```代码块中。"

        # 用户问题
        user_question = f"问题: {question}\n\n请生成对应的SQL查询："

        # 生成SQL
        try:
            response = vn.submit_prompt([
                vn.system_message(system_message),
                vn.user_message(user_question)
            ], model=text_model)
            
            print(f"🤖 AI回复:")
            print(response)
            
            # 提取SQL
            import re
            sql_match = re.search(r'```sql\s*(.*?)```', response, re.DOTALL | re.IGNORECASE)
            if sql_match:
                sql = sql_match.group(1).strip()
            else:
                sql_match = re.search(r'```\s*(.*?)```', response, re.DOTALL)
                if sql_match:
                    sql = sql_match.group(1).strip()
                else:
                    sql_match = re.search(r'(SELECT.*?;)', response, re.DOTALL | re.IGNORECASE)
                    if sql_match:
                        sql = sql_match.group(1).strip()
                    else:
                        sql = response.strip()
            
            print(f"\n📝 提取的SQL:")
            print(sql)
            
            # 测试执行
            if sql and sql.strip().lower().startswith('select'):
                try:
                    df = pd.read_sql_query(sql, conn)
                    print(f"\n✅ SQL执行成功！返回 {len(df)} 行结果")
                    if len(df) > 0:
                        print("📊 示例结果（前5行）:")
                        print(df.head().to_string(index=False))
                except Exception as e:
                    print(f"\n❌ SQL执行失败: {e}")
            else:
                print("\n⚠️ 未生成有效的SELECT查询")
                
        except Exception as e:
            print(f"❌ 生成SQL时出错: {e}")
    
    conn.close()
    print(f"\n{'='*80}")
    print("🎉 测试完成！")

if __name__ == '__main__':
    test_sql_generation()
